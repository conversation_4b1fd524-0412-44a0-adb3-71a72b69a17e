import { Router } from 'express';
import { body, param } from 'express-validator';
import { OrderController } from '../controllers/orderController';
import { authenticate } from '../middleware/auth';

const router = Router();

// Validation rules for creating orders
const createOrderValidation = [
  body('supplierId')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Supplier ID is required'),
  body('supplierName')
    .notEmpty()
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Supplier name is required and must be between 1 and 100 characters'),
  body('items')
    .isArray({ min: 1 })
    .withMessage('Items array is required and must contain at least one item'),
  body('items.*.productId')
    .notEmpty()
    .isString()
    .withMessage('Product ID is required for each item'),
  body('items.*.productName')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Product name is required for each item'),
  body('items.*.productImage')
    .optional()
    .isString()
    .withMessage('Product image must be a string'),
  body('items.*.quantity')
    .isInt({ min: 1 })
    .withMessage('Quantity must be a positive integer'),
  body('items.*.price')
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
  body('items.*.selectedOptions')
    .optional()
    .isObject()
    .withMessage('Selected options must be an object'),
  body('items.*.subtotal')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Item subtotal must be a positive number'),
  body('subtotal')
    .isFloat({ min: 0 })
    .withMessage('Subtotal must be a positive number'),
  body('deliveryFee')
    .isFloat({ min: 0 })
    .withMessage('Delivery fee must be a positive number'),
  body('totalAmount')
    .isFloat({ min: 0 })
    .withMessage('Total amount must be a positive number'),
  body('paymentMethod')
    .isIn(['cash', 'card', 'wallet'])
    .withMessage('Payment method must be cash, card, or wallet'),
  body('deliveryAddress.street')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Delivery street address is required'),
  body('deliveryAddress.city')
    .notEmpty()
    .isString()
    .trim()
    .withMessage('Delivery city is required'),
  body('deliveryAddress.coordinates.lat')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be a valid number between -90 and 90'),
  body('deliveryAddress.coordinates.lng')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be a valid number between -180 and 180'),
  body('customerPhone')
    .optional()
    .isString()
    .trim()
    .withMessage('Customer phone must be a string'),
  body('notes')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Notes must be a string with maximum 500 characters')
];

// Validation for updating order status
const updateOrderStatusValidation = [
  param('orderId')
    .notEmpty()
    .isString()
    .withMessage('Order ID is required'),
  body('status')
    .isIn(['pending', 'confirmed', 'preparing', 'ready', 'out_for_delivery', 'delivered', 'cancelled'])
    .withMessage('Status must be a valid order status'),
  body('trackingUpdate.message')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Tracking message must be a string with maximum 200 characters'),
  body('trackingUpdate.location.lat')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be a valid number between -90 and 90'),
  body('trackingUpdate.location.lng')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be a valid number between -180 and 180')
];

// All routes require authentication
router.use(authenticate);

// Order routes
router.post('/', createOrderValidation, OrderController.createOrder);
router.get('/', OrderController.getUserOrders);
router.get('/:orderId', OrderController.getOrderById);
router.put('/:orderId/status', updateOrderStatusValidation, OrderController.updateOrderStatus);

export default router;
