import mongoose, { Document, Schema } from 'mongoose';

export interface IPackage extends Document {
  packageId: string;
  userId: string;
  senderName: string;
  senderPhone: string;
  recipientName: string;
  recipientPhone: string;
  pickupAddress: {
    street: string;
    city: string;
    coordinates: {
      lat: number;
      lng: number;
    };
    notes?: string;
  };
  deliveryAddress: {
    street: string;
    city: string;
    coordinates: {
      lat: number;
      lng: number;
    };
    notes?: string;
  };
  packageDetails: {
    description: string;
    weight?: number;
    dimensions?: {
      length: number;
      width: number;
      height: number;
    };
    value?: number;
    fragile: boolean;
  };
  status: 'pending' | 'confirmed' | 'picked_up' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'cancelled';
  priority: 'standard' | 'express' | 'urgent';
  cost: number;
  paymentMethod: 'cash' | 'card' | 'wallet';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  scheduledPickupTime?: Date;
  actualPickupTime?: Date;
  estimatedDeliveryTime?: Date;
  actualDeliveryTime?: Date;
  driverId?: string;
  driverName?: string;
  driverPhone?: string;
  trackingUpdates: Array<{
    status: string;
    timestamp: Date;
    message: string;
    location?: {
      lat: number;
      lng: number;
    };
  }>;
  photos?: {
    pickup?: string[];
    delivery?: string[];
  };
  signature?: {
    recipientSignature?: string;
    recipientName?: string;
    timestamp?: Date;
  };
  notes?: string;
  rating?: {
    score: number;
    comment?: string;
    ratedAt: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

const PackageSchema: Schema = new Schema({
  packageId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  userId: {
    type: String,
    required: true,
    trim: true
  },
  senderName: {
    type: String,
    required: true,
    trim: true
  },
  senderPhone: {
    type: String,
    required: true,
    trim: true
  },
  recipientName: {
    type: String,
    required: true,
    trim: true
  },
  recipientPhone: {
    type: String,
    required: true,
    trim: true
  },
  pickupAddress: {
    street: { type: String, required: true, trim: true },
    city: { type: String, required: true, trim: true },
    coordinates: {
      lat: { type: Number, required: true },
      lng: { type: Number, required: true }
    },
    notes: { type: String, trim: true }
  },
  deliveryAddress: {
    street: { type: String, required: true, trim: true },
    city: { type: String, required: true, trim: true },
    coordinates: {
      lat: { type: Number, required: true },
      lng: { type: Number, required: true }
    },
    notes: { type: String, trim: true }
  },
  packageDetails: {
    description: { type: String, required: true, trim: true },
    weight: { type: Number, min: 0 },
    dimensions: {
      length: { type: Number, min: 0 },
      width: { type: Number, min: 0 },
      height: { type: Number, min: 0 }
    },
    value: { type: Number, min: 0 },
    fragile: { type: Boolean, default: false }
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'cancelled'],
    default: 'pending'
  },
  priority: {
    type: String,
    enum: ['standard', 'express', 'urgent'],
    default: 'standard'
  },
  cost: {
    type: Number,
    required: true,
    min: 0
  },
  paymentMethod: {
    type: String,
    enum: ['cash', 'card', 'wallet'],
    required: true
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'failed', 'refunded'],
    default: 'pending'
  },
  scheduledPickupTime: {
    type: Date
  },
  actualPickupTime: {
    type: Date
  },
  estimatedDeliveryTime: {
    type: Date
  },
  actualDeliveryTime: {
    type: Date
  },
  driverId: {
    type: String,
    trim: true
  },
  driverName: {
    type: String,
    trim: true
  },
  driverPhone: {
    type: String,
    trim: true
  },
  trackingUpdates: [{
    status: { type: String, required: true },
    timestamp: { type: Date, required: true },
    message: { type: String, required: true, trim: true },
    location: {
      lat: { type: Number },
      lng: { type: Number }
    }
  }],
  photos: {
    pickup: [{ type: String }],
    delivery: [{ type: String }]
  },
  signature: {
    recipientSignature: { type: String },
    recipientName: { type: String, trim: true },
    timestamp: { type: Date }
  },
  notes: {
    type: String,
    trim: true
  },
  rating: {
    score: { type: Number, min: 1, max: 5 },
    comment: { type: String, trim: true },
    ratedAt: { type: Date }
  }
}, {
  timestamps: true
});

// Indexes for efficient queries
PackageSchema.index({ userId: 1, createdAt: -1 });
PackageSchema.index({ status: 1, createdAt: -1 });
// Note: packageId index is automatically created by unique: true
PackageSchema.index({ driverId: 1, status: 1 });

export const Package = mongoose.model<IPackage>('Package', PackageSchema);
