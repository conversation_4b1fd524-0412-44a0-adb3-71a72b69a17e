const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');

// Load environment variables
dotenv.config();

// Import the database connection
const connectDB = require('../src/config/database');

// Import routes
const suppliersRoutes = require('../src/routes/suppliers');

const app = express();
const PORT = 5000;

// Connect to MongoDB
connectDB();

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.use('/api/suppliers', suppliersRoutes);

// Test endpoint
app.get('/test', (req, res) => {
  res.json({ message: 'API is working!' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Test server running on http://localhost:${PORT}`);
  console.log(`📝 Test endpoints:`);
  console.log(`   - GET http://localhost:${PORT}/test`);
  console.log(`   - GET http://localhost:${PORT}/api/suppliers/3a-kefak`);
  console.log(`   - GET http://localhost:${PORT}/api/suppliers/3a-kefak/products`);
});

module.exports = app;
