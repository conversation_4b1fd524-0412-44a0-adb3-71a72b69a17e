const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/wasel');
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Supplier Schema (simplified for this script)
const SupplierSchema = new mongoose.Schema({
  id: String,
  name: String,
  category: String,
  products: [{
    id: String,
    name: String,
    image: String,
    price: Number,
    discountPrice: Number,
    category: String,
    description: String,
    isAvailable: { type: Boolean, default: true },
    restaurantOptions: {
      additions: [{
        id: String,
        name: String,
        price: Number
      }],
      without: [String],
      sides: [{
        id: String,
        name: String,
        price: Number
      }]
    }
  }]
});

const Supplier = mongoose.model('Supplier', SupplierSchema);

// Sample products for "3a kefak" restaurant
const sampleProducts = [
  {
    id: 'shawarma-beef-001',
    name: 'شاورما لحم',
    image: 'https://images.unsplash.com/photo-1529006557810-274b9b2fc783?w=400&h=400&fit=crop',
    price: 18,
    discountPrice: 16,
    category: 'Shawarma',
    description: 'شاورما لحم طازج مع الخضار والصوص الخاص',
    isAvailable: true,
    restaurantOptions: {
      additions: [
        { id: 'extra-meat', name: 'زيادة لحم', price: 5 },
        { id: 'extra-sauce', name: 'زيادة صوص', price: 2 },
        { id: 'extra-pickles', name: 'زيادة مخللات', price: 1 }
      ],
      without: ['بصل', 'مخللات', 'طماطم', 'خيار'],
      sides: [
        { id: 'fries', name: 'بطاطا مقلية', price: 8 },
        { id: 'drink', name: 'مشروب غازي', price: 5 },
        { id: 'hummus', name: 'حمص', price: 6 }
      ]
    }
  },
  {
    id: 'shawarma-chicken-001',
    name: 'شاورما دجاج',
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=400&fit=crop',
    price: 15,
    category: 'Shawarma',
    description: 'شاورما دجاج طازج مع الخضار والثوم',
    isAvailable: true,
    restaurantOptions: {
      additions: [
        { id: 'extra-chicken', name: 'زيادة دجاج', price: 4 },
        { id: 'extra-garlic', name: 'زيادة ثوم', price: 1 },
        { id: 'extra-sauce', name: 'زيادة صوص', price: 2 }
      ],
      without: ['بصل', 'مخللات', 'طماطم'],
      sides: [
        { id: 'fries', name: 'بطاطا مقلية', price: 8 },
        { id: 'drink', name: 'مشروب غازي', price: 5 },
        { id: 'tabbouleh', name: 'تبولة', price: 7 }
      ]
    }
  },
  {
    id: 'falafel-sandwich-001',
    name: 'ساندويش فلافل',
    image: 'https://images.unsplash.com/photo-1593504049359-74330189a345?w=400&h=400&fit=crop',
    price: 12,
    category: 'Sandwiches',
    description: 'فلافل طازج مع السلطة والطحينة',
    isAvailable: true,
    restaurantOptions: {
      additions: [
        { id: 'extra-falafel', name: 'زيادة فلافل', price: 3 },
        { id: 'extra-tahini', name: 'زيادة طحينة', price: 1 }
      ],
      without: ['بصل', 'مخللات', 'طماطم', 'خيار'],
      sides: [
        { id: 'fries', name: 'بطاطا مقلية', price: 8 },
        { id: 'drink', name: 'مشروب غازي', price: 5 }
      ]
    }
  },
  {
    id: 'mixed-grill-001',
    name: 'مشاوي مشكلة',
    image: 'https://images.unsplash.com/photo-1544025162-d76694265947?w=400&h=400&fit=crop',
    price: 35,
    discountPrice: 32,
    category: 'Grills',
    description: 'مشاوي مشكلة مع الأرز والسلطة',
    isAvailable: true,
    restaurantOptions: {
      additions: [
        { id: 'extra-meat', name: 'زيادة لحم', price: 8 },
        { id: 'extra-rice', name: 'زيادة أرز', price: 4 }
      ],
      without: ['بصل مشوي', 'فلفل حار'],
      sides: [
        { id: 'salad', name: 'سلطة', price: 6 },
        { id: 'bread', name: 'خبز', price: 3 },
        { id: 'drink', name: 'مشروب غازي', price: 5 }
      ]
    }
  },
  {
    id: 'hummus-plate-001',
    name: 'صحن حمص',
    image: 'https://images.unsplash.com/photo-1571197119282-7c4e2b8b3d8e?w=400&h=400&fit=crop',
    price: 14,
    category: 'Appetizers',
    description: 'حمص طازج مع زيت الزيتون والصنوبر',
    isAvailable: true,
    restaurantOptions: {
      additions: [
        { id: 'extra-pine-nuts', name: 'زيادة صنوبر', price: 3 },
        { id: 'extra-olive-oil', name: 'زيادة زيت زيتون', price: 1 }
      ],
      without: ['صنوبر', 'بقدونس'],
      sides: [
        { id: 'bread', name: 'خبز', price: 3 },
        { id: 'pickles', name: 'مخللات', price: 4 }
      ]
    }
  },
  {
    id: 'fresh-juice-001',
    name: 'عصير طازج',
    image: 'https://images.unsplash.com/photo-1546173159-315724a31696?w=400&h=400&fit=crop',
    price: 8,
    category: 'Beverages',
    description: 'عصير فواكه طازج (برتقال، تفاح، جزر)',
    isAvailable: true,
    restaurantOptions: {
      additions: [
        { id: 'extra-sugar', name: 'زيادة سكر', price: 0 },
        { id: 'ice', name: 'ثلج', price: 0 }
      ],
      without: ['سكر', 'ثلج'],
      sides: []
    }
  }
];

async function addSampleProducts() {
  try {
    await connectDB();

    // Find the "3a kefak" supplier (you might need to adjust the query based on your data)
    const supplier = await Supplier.findOne({ 
      $or: [
        { name: /3a kefak/i },
        { name: /كيفك/i },
        { id: '3a-kefak' }
      ]
    });

    if (!supplier) {
      console.log('❌ Supplier "3a kefak" not found. Available suppliers:');
      const allSuppliers = await Supplier.find({}, 'id name category');
      allSuppliers.forEach(s => {
        console.log(`  - ID: ${s.id}, Name: ${s.name}, Category: ${s.category}`);
      });
      process.exit(1);
    }

    console.log(`✅ Found supplier: ${supplier.name} (ID: ${supplier.id})`);

    // Add products to the supplier
    supplier.products = sampleProducts;
    await supplier.save();

    console.log(`✅ Successfully added ${sampleProducts.length} products to ${supplier.name}`);
    console.log('Products added:');
    sampleProducts.forEach(product => {
      console.log(`  - ${product.name} (${product.category}) - ₪${product.price}`);
    });

    process.exit(0);
  } catch (error) {
    console.error('❌ Error adding sample products:', error);
    process.exit(1);
  }
}

// Run the script
addSampleProducts();
