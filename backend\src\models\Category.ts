import mongoose, { Document, Schema } from 'mongoose';

export interface ICategory extends Document {
  key: string;
  label: string;
  icon: string;
  color: string;
  image?: string;
  bgGradient?: string;
  shadowColor?: string;
  subtitle?: string;
  badge?: string;
  badgeColor?: string;
  route: {
    pathname: string;
    params: {
      category: string;
    };
  };
  isActive: boolean;
  order: number;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

const CategorySchema: Schema = new Schema({
  key: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  label: {
    type: String,
    required: true,
    trim: true
  },
  icon: {
    type: String,
    required: true,
    trim: true
  },
  color: {
    type: String,
    required: true,
    trim: true
  },
  image: {
    type: String,
    trim: true
  },
  bgGradient: {
    type: String,
    trim: true
  },
  shadowColor: {
    type: String,
    trim: true
  },
  subtitle: {
    type: String,
    trim: true
  },
  badge: {
    type: String,
    trim: true
  },
  badgeColor: {
    type: String,
    trim: true
  },
  route: {
    pathname: {
      type: String,
      required: true,
      trim: true
    },
    params: {
      category: {
        type: String,
        required: true,
        trim: true
      }
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  order: {
    type: Number,
    default: 0
  },
  description: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Index for efficient queries
CategorySchema.index({ isActive: 1, order: 1 });

export const Category = mongoose.model<ICategory>('Category', CategorySchema);
